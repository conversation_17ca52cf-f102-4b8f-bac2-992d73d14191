@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        direction: rtl;
        font-family: 'IBM Plex Sans Arabic', sans-serif;
    }

    body {
        @apply font-arabic text-secondary-800 bg-gray-50;
    }

    /* RTL specific adjustments */
    .rtl-flip {
        transform: scaleX(-1);
    }

    /* Custom scrollbar for RTL */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        @apply bg-gray-100;
    }

    ::-webkit-scrollbar-thumb {
        @apply bg-gray-300 rounded-full;
    }

    ::-webkit-scrollbar-thumb:hover {
        @apply bg-gray-400;
    }
}

@layer components {
    /* Modern Admin Panel Components */
    .admin-card {
        @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200/50 p-6 lg:p-8 transition-all duration-200 hover:shadow-lg hover:bg-white/90;
    }

    .admin-button {
        @apply inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 border border-transparent rounded-xl font-semibold text-sm text-white shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 active:scale-95 transition-all duration-200;
    }

    .admin-button-secondary {
        @apply inline-flex items-center px-6 py-3 bg-white border border-slate-300 rounded-xl font-semibold text-sm text-slate-700 shadow-sm hover:bg-slate-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200;
    }

    .admin-button-danger {
        @apply inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 border border-transparent rounded-xl font-semibold text-sm text-white shadow-lg hover:shadow-xl hover:from-red-700 hover:to-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 active:scale-95 transition-all duration-200;
    }

    .admin-input {
        @apply border-slate-300 focus:border-blue-500 focus:ring-blue-500 rounded-xl shadow-sm bg-white/50 backdrop-blur-sm transition-all duration-200 focus:bg-white focus:shadow-lg;
    }

    .admin-select {
        @apply border-slate-300 focus:border-blue-500 focus:ring-blue-500 rounded-xl shadow-sm bg-white/50 backdrop-blur-sm transition-all duration-200 focus:bg-white focus:shadow-lg;
    }

    .admin-textarea {
        @apply border-slate-300 focus:border-blue-500 focus:ring-blue-500 rounded-xl shadow-sm bg-white/50 backdrop-blur-sm transition-all duration-200 focus:bg-white focus:shadow-lg;
    }

    /* Modern Navigation Links */
    .nav-link {
        @apply flex items-center p-4 rounded-2xl text-slate-600 hover:text-slate-900 hover:bg-slate-50/80 transition-all duration-200 hover:shadow-sm;
    }

    .nav-link-active {
        @apply text-blue-600 bg-blue-50/80 shadow-sm border-r-4 border-blue-500;
    }

    /* Enhanced Status Badges */
    .status-badge {
        @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold shadow-sm;
    }

    .status-active {
        @apply bg-emerald-100 text-emerald-800 border border-emerald-200;
    }

    .status-inactive {
        @apply bg-red-100 text-red-800 border border-red-200;
    }

    .status-pending {
        @apply bg-amber-100 text-amber-800 border border-amber-200;
    }

    .status-completed {
        @apply bg-blue-100 text-blue-800 border border-blue-200;
    }

    /* Modern Card Variants */
    .stats-card {
        @apply bg-gradient-to-br from-white to-slate-50/50 rounded-2xl shadow-sm border border-slate-200/50 p-6 transition-all duration-200 hover:shadow-lg hover:scale-105;
    }

    .glass-card {
        @apply bg-white/60 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20;
    }

    /* Interactive Elements */
    .interactive-card {
        @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-1 cursor-pointer;
    }

    /* Form Enhancements */
    .form-group {
        @apply space-y-2;
    }

    .form-label {
        @apply block text-sm font-semibold text-slate-700 mb-2;
    }

    .form-error {
        @apply text-sm text-red-600 mt-1;
    }

    .form-help {
        @apply text-sm text-slate-500 mt-1;
    }

    /* Table Enhancements */
    .modern-table {
        @apply min-w-full divide-y divide-slate-200 bg-white/50 backdrop-blur-sm rounded-2xl overflow-hidden shadow-sm;
    }

    .table-header {
        @apply bg-slate-50/80 backdrop-blur-sm;
    }

    .table-row {
        @apply hover:bg-slate-50/50 transition-colors duration-150;
    }

    /* Loading States */
    .skeleton {
        @apply animate-pulse bg-slate-200 rounded;
    }

    .shimmer {
        @apply relative overflow-hidden bg-slate-200 rounded;
    }

    .shimmer::after {
        @apply absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/60 to-transparent;
        content: '';
        animation: shimmer 2s infinite;
    }

    /* Responsive Utilities */
    .mobile-only {
        @apply block sm:hidden;
    }

    .desktop-only {
        @apply hidden lg:block;
    }

    .tablet-up {
        @apply hidden sm:block;
    }
}

/* Custom Animations */
@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Page Transition */
.page-transition {
    animation: fadeIn 0.3s ease-out;
}

/* Sidebar Animation */
.sidebar-enter {
    animation: slideIn 0.3s ease-out;
}

/* Focus Styles for Accessibility */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}
