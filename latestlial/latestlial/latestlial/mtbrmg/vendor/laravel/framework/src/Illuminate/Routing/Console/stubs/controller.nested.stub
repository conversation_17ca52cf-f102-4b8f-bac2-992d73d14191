<?php

namespace {{ namespace }};

use {{ namespacedModel }};
use {{ rootNamespace }}Http\Controllers\Controller;
use {{ namespacedParentModel }};
use Illuminate\Http\Request;

class {{ class }} extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index({{ parentModel }} ${{ parentModelVariable }})
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create({{ parentModel }} ${{ parentModelVariable }})
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, {{ parentModel }} ${{ parentModelVariable }})
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show({{ parentModel }} ${{ parentModelVariable }}, {{ model }} ${{ modelVariable }})
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit({{ parentModel }} ${{ parentModelVariable }}, {{ model }} ${{ modelVariable }})
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, {{ parentModel }} ${{ parentModelVariable }}, {{ model }} ${{ modelVariable }})
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy({{ parentModel }} ${{ parentModelVariable }}, {{ model }} ${{ modelVariable }})
    {
        //
    }
}
